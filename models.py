from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class Employee(db.Model):
    __tablename__ = 'employees'
    
    id = db.Column(db.Integer, primary_key=True)
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    date_of_birth = db.Column(db.Date, nullable=False)
    place_of_birth = db.Column(db.String(200), nullable=False)
    position = db.Column(db.String(100), nullable=False)
    workplace = db.Column(db.String(200), nullable=False)
    employment_date = db.Column(db.Date, nullable=False)
    salary_amount = db.Column(db.Float, nullable=False)
    bank_account_number = db.Column(db.String(50), nullable=False)
    bank_name = db.Column(db.String(100), nullable=False)
    agency = db.Column(db.String(100), nullable=False)
    
    # Relationship with children
    children = db.relationship('Child', backref='employee', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Employee {self.first_name} {self.last_name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'date_of_birth': self.date_of_birth.strftime('%Y-%m-%d') if self.date_of_birth else None,
            'place_of_birth': self.place_of_birth,
            'position': self.position,
            'workplace': self.workplace,
            'employment_date': self.employment_date.strftime('%Y-%m-%d') if self.employment_date else None,
            'salary_amount': self.salary_amount,
            'bank_account_number': self.bank_account_number,
            'bank_name': self.bank_name,
            'agency': self.agency
        }

class Child(db.Model):
    __tablename__ = 'children'
    
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    date_of_birth = db.Column(db.Date, nullable=False)
    educational_level = db.Column(db.String(100), nullable=False)
    
    def __repr__(self):
        return f'<Child {self.name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'name': self.name,
            'date_of_birth': self.date_of_birth.strftime('%Y-%m-%d') if self.date_of_birth else None,
            'educational_level': self.educational_level
        }

class Activation(db.Model):
    __tablename__ = 'activation'
    
    id = db.Column(db.Integer, primary_key=True)
    install_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    hdd_serial = db.Column(db.String(100), nullable=False)
    expiration_date = db.Column(db.DateTime, nullable=False)
    
    def __repr__(self):
        return f'<Activation {self.hdd_serial}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'install_date': self.install_date.strftime('%Y-%m-%d %H:%M:%S'),
            'hdd_serial': self.hdd_serial,
            'expiration_date': self.expiration_date.strftime('%Y-%m-%d %H:%M:%S')
        }

def init_db(app):
    """Initialize the database with the Flask app"""
    db.init_app(app)
    
    with app.app_context():
        db.create_all()
        print("Database tables created successfully!")
