@echo off
echo ========================================
echo Employee Management System - Trial Version
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8 or higher from:
    echo https://www.python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo Python found. Checking dependencies...

REM Install dependencies
echo Installing required packages...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to install dependencies
    echo Please make sure pip is working correctly
    echo.
    pause
    exit /b 1
)

echo.
echo Starting Employee Management System...
echo.
echo The application will open in your default web browser
echo at http://127.0.0.1:5000
echo.
echo Press Ctrl+C to stop the application
echo.

REM Start the Flask application
python app.py

pause
