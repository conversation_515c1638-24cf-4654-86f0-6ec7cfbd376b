@echo off
chcp 65001 >nul
echo ========================================
echo نظام إدارة الموظفين - النسخة التجريبية
echo Employee Management System - Trial Version
echo ========================================
echo.

echo التحقق من Python...
py --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo ❌ Python is not installed or not in PATH
    echo.
    echo يرجى تثبيت Python 3.8 أو أحدث من:
    echo Please install Python 3.8 or higher from:
    echo https://www.python.org/downloads/
    echo.
    echo تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo ✅ Python found
echo.

echo تثبيت المتطلبات...
echo Installing requirements...
py -m pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في تثبيت المتطلبات
    echo ❌ Failed to install requirements
    echo.
    echo محاولة التثبيت البديل...
    echo Trying alternative installation...
    py -m pip install Flask SQLAlchemy Flask-SQLAlchemy Werkzeug
    
    if %errorlevel% neq 0 (
        echo.
        echo ❌ فشل في تثبيت المكتبات المطلوبة
        echo ❌ Failed to install required libraries
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ✅ تم تثبيت المتطلبات بنجاح
echo ✅ Requirements installed successfully
echo.

echo بدء تشغيل نظام إدارة الموظفين...
echo Starting Employee Management System...
echo.
echo سيكون التطبيق متاحاً على:
echo The application will be available at:
echo http://127.0.0.1:5000
echo.
echo اضغط Ctrl+C لإيقاف التطبيق
echo Press Ctrl+C to stop the application
echo ========================================
echo.

py app.py

echo.
echo تم إيقاف التطبيق
echo Application stopped
pause
