<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الموظفين{% endblock %}</title>
    
    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: 700;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .btn {
            border-radius: 10px;
        }
        .trial-status {
            font-weight: 600;
        }
        .trial-warning {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-users"></i> نظام إدارة الموظفين
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('employees') }}">
                            <i class="fas fa-user-tie"></i> الموظفين
                        </a>
                    </li>
                </ul>
                
                <!-- Trial Status -->
                <div class="navbar-text text-light trial-status" id="trial-status">
                    <i class="fas fa-clock"></i> جاري تحميل معلومات التجربة...
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p class="text-muted mb-0">
                <i class="fas fa-shield-alt"></i> 
                نظام إدارة الموظفين - النسخة التجريبية
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Update trial status
        function updateTrialStatus() {
            fetch('/api/activation-info')
                .then(response => response.json())
                .then(data => {
                    if (data) {
                        const daysRemaining = data.days_remaining;
                        const hoursRemaining = data.hours_remaining;
                        const isExpired = data.is_expired;
                        
                        let statusText = '';
                        let statusClass = 'trial-status text-light';
                        
                        if (isExpired) {
                            statusText = '<i class="fas fa-exclamation-triangle"></i> انتهت التجربة';
                            statusClass = 'trial-status text-danger trial-warning';
                        } else if (daysRemaining === 0) {
                            statusText = `<i class="fas fa-clock"></i> ${hoursRemaining} ساعة متبقية`;
                            statusClass = 'trial-status text-warning trial-warning';
                        } else if (daysRemaining <= 1) {
                            statusText = `<i class="fas fa-clock"></i> ${daysRemaining} يوم ${hoursRemaining} ساعة متبقية`;
                            statusClass = 'trial-status text-warning';
                        } else {
                            statusText = `<i class="fas fa-clock"></i> ${daysRemaining} أيام متبقية`;
                            statusClass = 'trial-status text-light';
                        }
                        
                        document.getElementById('trial-status').innerHTML = statusText;
                        document.getElementById('trial-status').className = statusClass;
                        
                        if (isExpired) {
                            setTimeout(() => {
                                window.location.href = '/activation-expired';
                            }, 2000);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error updating trial status:', error);
                });
        }
        
        // Update trial status on page load and every minute
        document.addEventListener('DOMContentLoaded', updateTrialStatus);
        setInterval(updateTrialStatus, 60000);
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
