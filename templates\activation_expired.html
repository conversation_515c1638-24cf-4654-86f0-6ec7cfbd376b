<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trial Expired - Employee Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .expired-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
            margin: 20px;
        }
        
        .expired-icon {
            font-size: 4rem;
            color: #dc3545;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .countdown {
            font-size: 2rem;
            font-weight: bold;
            color: #dc3545;
        }
        
        .progress-bar {
            animation: countdown 10s linear forwards;
        }
        
        @keyframes countdown {
            from { width: 100%; }
            to { width: 0%; }
        }
    </style>
</head>
<body>
    <div class="expired-card">
        <div class="card-body text-center p-5">
            <!-- Expired Icon -->
            <div class="mb-4">
                <i class="fas fa-exclamation-triangle expired-icon"></i>
            </div>
            
            <!-- Title -->
            <h1 class="text-danger mb-3">Trial Expired</h1>
            
            <!-- Message -->
            <p class="lead text-muted mb-4">
                Your 3-day trial period for the Employee Management System has expired.
            </p>
            
            <!-- Details -->
            <div class="alert alert-danger" role="alert">
                <h5 class="alert-heading">
                    <i class="fas fa-clock"></i> Trial Period Ended
                </h5>
                <p class="mb-0">
                    The application will now close and all files will be automatically deleted 
                    as per the trial agreement.
                </p>
            </div>
            
            <!-- Countdown -->
            <div class="mb-4">
                <p class="text-muted">Application will close in:</p>
                <div class="countdown" id="countdown">10</div>
                <div class="progress mt-3">
                    <div class="progress-bar bg-danger progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 100%"></div>
                </div>
            </div>
            
            <!-- Information -->
            <div class="text-start">
                <h6 class="text-primary">
                    <i class="fas fa-info-circle"></i> What happens next:
                </h6>
                <ul class="text-muted small">
                    <li>Application files will be automatically deleted</li>
                    <li>All employee and children data will be removed</li>
                    <li>Database will be permanently deleted</li>
                    <li>No recovery will be possible</li>
                </ul>
            </div>
            
            <!-- Contact Info -->
            <div class="mt-4 p-3 bg-light rounded">
                <h6 class="text-success mb-2">
                    <i class="fas fa-shopping-cart"></i> Interested in the full version?
                </h6>
                <p class="small text-muted mb-0">
                    Contact us for licensing information and to purchase the full version 
                    with unlimited usage and additional features.
                </p>
            </div>
            
            <!-- Close Button -->
            <div class="mt-4">
                <button class="btn btn-danger" onclick="closeApplication()">
                    <i class="fas fa-times"></i> Close Now
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let countdown = 10;
        const countdownElement = document.getElementById('countdown');
        
        function updateCountdown() {
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                closeApplication();
                return;
            }
            
            countdown--;
            setTimeout(updateCountdown, 1000);
        }
        
        function closeApplication() {
            // Try to close the window/tab
            if (window.opener) {
                window.close();
            } else {
                // If we can't close, redirect to a blank page
                document.body.innerHTML = `
                    <div class="text-center p-5">
                        <h2 class="text-danger">Application Terminated</h2>
                        <p class="text-muted">You can now close this window.</p>
                        <p class="small text-muted">Files are being deleted automatically...</p>
                    </div>
                `;
                
                // Try to close after a delay
                setTimeout(() => {
                    window.close();
                }, 2000);
            }
        }
        
        // Start countdown when page loads
        document.addEventListener('DOMContentLoaded', function() {
            updateCountdown();
        });
        
        // Prevent user from navigating away
        window.addEventListener('beforeunload', function(e) {
            e.preventDefault();
            e.returnValue = 'Trial has expired. Application is closing.';
        });
        
        // Disable right-click context menu
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        // Disable F12, Ctrl+Shift+I, etc.
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>
