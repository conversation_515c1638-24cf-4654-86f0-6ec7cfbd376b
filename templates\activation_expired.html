<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>انتهت التجربة - نظام إدارة الموظفين</title>
    
    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .expired-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
            margin: 20px;
        }
        
        .expired-icon {
            font-size: 4rem;
            color: #dc3545;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .countdown {
            font-size: 2rem;
            font-weight: bold;
            color: #dc3545;
        }
        
        .progress-bar {
            animation: countdown 10s linear forwards;
        }
        
        @keyframes countdown {
            from { width: 100%; }
            to { width: 0%; }
        }
    </style>
</head>
<body>
    <div class="expired-card">
        <div class="card-body text-center p-5">
            <!-- Expired Icon -->
            <div class="mb-4">
                <i class="fas fa-exclamation-triangle expired-icon"></i>
            </div>
            
            <!-- Title -->
            <h1 class="text-danger mb-3">انتهت فترة التجربة</h1>
            
            <!-- Message -->
            <p class="lead text-muted mb-4">
                انتهت فترة التجربة المجانية لمدة 3 أيام لنظام إدارة الموظفين.
            </p>
            
            <!-- Details -->
            <div class="alert alert-danger" role="alert">
                <h5 class="alert-heading">
                    <i class="fas fa-clock"></i> انتهت فترة التجربة
                </h5>
                <p class="mb-0">
                    سيتم الآن إغلاق التطبيق وحذف جميع الملفات تلقائياً 
                    وفقاً لاتفاقية التجربة.
                </p>
            </div>
            
            <!-- Countdown -->
            <div class="mb-4">
                <p class="text-muted">سيتم إغلاق التطبيق خلال:</p>
                <div class="countdown" id="countdown">10</div>
                <div class="progress mt-3">
                    <div class="progress-bar bg-danger progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 100%"></div>
                </div>
            </div>
            
            <!-- Information -->
            <div class="text-start">
                <h6 class="text-primary">
                    <i class="fas fa-info-circle"></i> ما سيحدث الآن:
                </h6>
                <ul class="text-muted small">
                    <li>سيتم حذف ملفات التطبيق تلقائياً</li>
                    <li>سيتم إزالة جميع بيانات الموظفين والأطفال</li>
                    <li>سيتم حذف قاعدة البيانات نهائياً</li>
                    <li>لن يكون بالإمكان استرداد أي بيانات</li>
                </ul>
            </div>
            
            <!-- Contact Info -->
            <div class="mt-4 p-3 bg-light rounded">
                <h6 class="text-success mb-2">
                    <i class="fas fa-shopping-cart"></i> مهتم بالنسخة الكاملة؟
                </h6>
                <p class="small text-muted mb-0">
                    تواصل معنا للحصول على معلومات الترخيص وشراء النسخة الكاملة 
                    مع الاستخدام غير المحدود والميزات الإضافية.
                </p>
            </div>
            
            <!-- Close Button -->
            <div class="mt-4">
                <button class="btn btn-danger" onclick="closeApplication()">
                    <i class="fas fa-times"></i> إغلاق الآن
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let countdown = 10;
        const countdownElement = document.getElementById('countdown');
        
        function updateCountdown() {
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                closeApplication();
                return;
            }
            
            countdown--;
            setTimeout(updateCountdown, 1000);
        }
        
        function closeApplication() {
            // Try to close the window/tab
            if (window.opener) {
                window.close();
            } else {
                // If we can't close, redirect to a blank page
                document.body.innerHTML = `
                    <div class="text-center p-5">
                        <h2 class="text-danger">تم إنهاء التطبيق</h2>
                        <p class="text-muted">يمكنك الآن إغلاق هذه النافذة.</p>
                        <p class="small text-muted">جاري حذف الملفات تلقائياً...</p>
                    </div>
                `;
                
                // Try to close after a delay
                setTimeout(() => {
                    window.close();
                }, 2000);
            }
        }
        
        // Start countdown when page loads
        document.addEventListener('DOMContentLoaded', function() {
            updateCountdown();
        });
        
        // Prevent user from navigating away
        window.addEventListener('beforeunload', function(e) {
            e.preventDefault();
            e.returnValue = 'انتهت فترة التجربة. جاري إغلاق التطبيق.';
        });
        
        // Disable right-click context menu
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        // Disable F12, Ctrl+Shift+I, etc.
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>
