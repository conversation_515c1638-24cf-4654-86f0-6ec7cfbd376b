#!/usr/bin/env python3
"""Quick test to check if the application can start"""

import sys
import traceback

print("=" * 50)
print("Quick Test - Employee Management System")
print("=" * 50)

try:
    print("1. Testing imports...")
    
    print("   - Importing Flask...")
    from flask import Flask
    print("   ✓ Flask imported")
    
    print("   - Importing models...")
    from models import db, Employee, Child, Activation, init_db
    print("   ✓ Models imported")
    
    print("   - Importing activation...")
    from activation import get_hdd_serial, check_activation
    print("   ✓ Activation imported")
    
    print("\n2. Testing hardware detection...")
    serial = get_hdd_serial()
    print(f"   ✓ HDD Serial: {serial[:15]}...")
    
    print("\n3. Creating test Flask app...")
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test-key'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    print("   ✓ Flask app created")
    
    print("\n4. Initializing database...")
    init_db(app)
    print("   ✓ Database initialized")
    
    print("\n5. Testing activation system...")
    with app.app_context():
        is_valid, message = check_activation()
        print(f"   ✓ Activation check: {is_valid} - {message}")
    
    print("\n6. Testing basic route...")
    @app.route('/')
    def test_route():
        return "Hello from Employee Management System!"
    
    print("   ✓ Route defined")
    
    print("\n" + "=" * 50)
    print("✅ ALL TESTS PASSED!")
    print("The application should work correctly.")
    print("=" * 50)
    
    print("\nStarting the application...")
    print("Access it at: http://127.0.0.1:5000")
    print("Press Ctrl+C to stop")
    print("-" * 50)
    
    # Start the application
    app.run(debug=True, host='127.0.0.1', port=5000)
    
except Exception as e:
    print(f"\n❌ ERROR: {e}")
    print("\nFull traceback:")
    traceback.print_exc()
    print("\n" + "=" * 50)
    print("❌ TEST FAILED")
    print("Please check the error above.")
    print("=" * 50)
    input("Press Enter to exit...")
    sys.exit(1)
