#!/usr/bin/env python3
"""
اختبار بسيط لنظام إدارة الموظفين
Simple test for Employee Management System
"""

print("=" * 50)
print("اختبار نظام إدارة الموظفين")
print("Employee Management System Test")
print("=" * 50)

try:
    print("1. اختبار استيراد Flask...")
    print("1. Testing Flask import...")
    from flask import Flask
    print("   ✅ تم استيراد Flask بنجاح")
    print("   ✅ Flask imported successfully")
    
    print("\n2. إنشاء تطبيق Flask بسيط...")
    print("2. Creating simple Flask app...")
    app = Flask(__name__)
    
    @app.route('/')
    def hello():
        return '''
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>نظام إدارة الموظفين</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>
                body { font-family: 'Cairo', sans-serif; }
                .hero { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4rem 0; }
            </style>
        </head>
        <body>
            <div class="hero">
                <div class="container text-center">
                    <h1 class="display-4">🎉 نظام إدارة الموظفين</h1>
                    <p class="lead">Employee Management System</p>
                    <p class="mb-0">التطبيق يعمل بنجاح! - Application is running successfully!</p>
                </div>
            </div>
            <div class="container mt-5">
                <div class="row">
                    <div class="col-md-6 mx-auto">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">✅ الاختبار نجح</h5>
                                <p class="card-text">Flask يعمل بشكل صحيح</p>
                                <a href="#" class="btn btn-primary">ممتاز!</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        '''
    
    print("   ✅ تم إنشاء التطبيق بنجاح")
    print("   ✅ App created successfully")
    
    print("\n3. بدء الخادم...")
    print("3. Starting server...")
    print("\n🌐 التطبيق متاح على:")
    print("🌐 Application available at:")
    print("   http://127.0.0.1:5000")
    print("\n⚠️  اضغط Ctrl+C لإيقاف الخادم")
    print("⚠️  Press Ctrl+C to stop the server")
    print("=" * 50)
    
    app.run(debug=True, host='127.0.0.1', port=5000)
    
except ImportError as e:
    print(f"\n❌ خطأ في الاستيراد: {e}")
    print(f"❌ Import error: {e}")
    print("\n💡 الحل:")
    print("💡 Solution:")
    print("   py -m pip install Flask")
    
except Exception as e:
    print(f"\n❌ خطأ: {e}")
    print(f"❌ Error: {e}")
    
finally:
    print("\n" + "=" * 50)
    print("انتهى الاختبار")
    print("Test completed")
    print("=" * 50)
