{% extends "base.html" %}

{% block title %}الأطفال - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-child"></i> أطفال - {{ employee.first_name }} {{ employee.last_name }}
            </h1>
            <a href="{{ url_for('employees') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة للموظفين
            </a>
        </div>
    </div>
</div>

<!-- Employee Info Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>الموظف:</strong><br>
                        {{ employee.first_name }} {{ employee.last_name }}
                    </div>
                    <div class="col-md-3">
                        <strong>المنصب:</strong><br>
                        {{ employee.position }}
                    </div>
                    <div class="col-md-3">
                        <strong>مكان العمل:</strong><br>
                        {{ employee.workplace }}
                    </div>
                    <div class="col-md-3">
                        <strong>عدد الأطفال:</strong><br>
                        {{ children|length }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Child Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus"></i> إضافة طفل جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('add_child', employee_id=employee.id) }}">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم الطفل *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="date_of_birth" class="form-label">تاريخ الميلاد *</label>
                                <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="educational_level" class="form-label">المستوى التعليمي *</label>
                                <select class="form-control" id="educational_level" name="educational_level" required>
                                    <option value="">اختر المستوى</option>
                                    <option value="روضة أطفال">روضة أطفال</option>
                                    <option value="ابتدائي">ابتدائي</option>
                                    <option value="متوسط">متوسط</option>
                                    <option value="ثانوي">ثانوي</option>
                                    <option value="جامعي">جامعي</option>
                                    <option value="دراسات عليا">دراسات عليا</option>
                                    <option value="غير ملتحق بالدراسة">غير ملتحق بالدراسة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة طفل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Children List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> قائمة الأطفال ({{ children|length }} طفل)
                </h5>
            </div>
            <div class="card-body">
                {% if children %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الرقم</th>
                                    <th>الاسم</th>
                                    <th>تاريخ الميلاد</th>
                                    <th>العمر</th>
                                    <th>المستوى التعليمي</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for child in children %}
                                <tr>
                                    <td>{{ child.id }}</td>
                                    <td>
                                        <strong>{{ child.name }}</strong>
                                    </td>
                                    <td>{{ child.date_of_birth.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <span class="age-cell" data-dob="{{ child.date_of_birth.strftime('%Y-%m-%d') }}">
                                            جاري الحساب...
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ child.educational_level }}</span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDeleteChild({{ child.id }}, '{{ child.name }}')"
                                                title="حذف الطفل">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-child fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد أطفال مسجلين</h5>
                        <p class="text-muted">استخدم النموذج أعلاه لإضافة أطفال لهذا الموظف.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Child Confirmation Modal -->
<div class="modal fade" id="deleteChildModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف <strong id="childName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    هذا الإجراء لا يمكن التراجع عنه.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteChildForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDeleteChild(childId, childName) {
    document.getElementById('childName').textContent = childName;
    document.getElementById('deleteChildForm').action = '/child/delete/' + childId;
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteChildModal'));
    deleteModal.show();
}

// Calculate age dynamically
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    
    document.querySelectorAll('.age-cell').forEach(ageCell => {
        const dob = new Date(ageCell.getAttribute('data-dob'));
        const age = Math.floor((today - dob) / (365.25 * 24 * 60 * 60 * 1000));
        ageCell.textContent = age + ' سنة';
    });
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const dob = document.getElementById('date_of_birth').value;
    const education = document.getElementById('educational_level').value;
    
    if (!name || !dob || !education) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة.');
        return;
    }
    
    // Check if date is not in the future
    const dobDate = new Date(dob);
    const today = new Date();
    
    if (dobDate > today) {
        e.preventDefault();
        alert('تاريخ الميلاد لا يمكن أن يكون في المستقبل.');
        return;
    }
});
</script>
{% endblock %}
