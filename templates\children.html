{% extends "base.html" %}

{% block title %}Children - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-child"></i> Children - {{ employee.first_name }} {{ employee.last_name }}
            </h1>
            <a href="{{ url_for('employees') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Employees
            </a>
        </div>
    </div>
</div>

<!-- Employee Info Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>Employee:</strong><br>
                        {{ employee.first_name }} {{ employee.last_name }}
                    </div>
                    <div class="col-md-3">
                        <strong>Position:</strong><br>
                        {{ employee.position }}
                    </div>
                    <div class="col-md-3">
                        <strong>Workplace:</strong><br>
                        {{ employee.workplace }}
                    </div>
                    <div class="col-md-3">
                        <strong>Children Count:</strong><br>
                        {{ children|length }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Child Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus"></i> Add New Child
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('add_child', employee_id=employee.id) }}">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="name" class="form-label">Child Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="date_of_birth" class="form-label">Date of Birth *</label>
                                <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="educational_level" class="form-label">Educational Level *</label>
                                <select class="form-control" id="educational_level" name="educational_level" required>
                                    <option value="">Select Level</option>
                                    <option value="Preschool">Preschool</option>
                                    <option value="Elementary">Elementary</option>
                                    <option value="Middle School">Middle School</option>
                                    <option value="High School">High School</option>
                                    <option value="College">College</option>
                                    <option value="University">University</option>
                                    <option value="Graduate">Graduate</option>
                                    <option value="Not in School">Not in School</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Child
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Children List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> Children List ({{ children|length }} total)
                </h5>
            </div>
            <div class="card-body">
                {% if children %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Date of Birth</th>
                                    <th>Age</th>
                                    <th>Educational Level</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for child in children %}
                                <tr>
                                    <td>{{ child.id }}</td>
                                    <td>
                                        <strong>{{ child.name }}</strong>
                                    </td>
                                    <td>{{ child.date_of_birth.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <span class="age-cell" data-dob="{{ child.date_of_birth.strftime('%Y-%m-%d') }}">
                                            Calculating...
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ child.educational_level }}</span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="confirmDeleteChild({{ child.id }}, '{{ child.name }}')"
                                                title="Delete Child">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-child fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">No Children Registered</h5>
                        <p class="text-muted">Use the form above to add children for this employee.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Child Confirmation Modal -->
<div class="modal fade" id="deleteChildModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="childName"></strong>?</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    This action cannot be undone.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteChildForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDeleteChild(childId, childName) {
    document.getElementById('childName').textContent = childName;
    document.getElementById('deleteChildForm').action = '/child/delete/' + childId;

    var deleteModal = new bootstrap.Modal(document.getElementById('deleteChildModal'));
    deleteModal.show();
}

// Calculate age dynamically
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();

    document.querySelectorAll('.age-cell').forEach(ageCell => {
        const dob = new Date(ageCell.getAttribute('data-dob'));
        const age = Math.floor((today - dob) / (365.25 * 24 * 60 * 60 * 1000));
        ageCell.textContent = age + ' years';
    });
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const dob = document.getElementById('date_of_birth').value;
    const education = document.getElementById('educational_level').value;

    if (!name || !dob || !education) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return;
    }

    // Check if date is not in the future
    const dobDate = new Date(dob);
    const today = new Date();

    if (dobDate > today) {
        e.preventDefault();
        alert('Date of birth cannot be in the future.');
        return;
    }
});
</script>
{% endblock %}
