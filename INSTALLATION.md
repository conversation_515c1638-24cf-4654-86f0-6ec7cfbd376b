# Employee Management System - Installation Guide

## 🚀 Quick Start

### For Windows Users
1. **Double-click `run.bat`** - This will automatically:
   - Check Python installation
   - Install dependencies
   - Start the application
   - Open your web browser

### For Linux/Mac Users
1. **Open terminal in this folder**
2. **Run**: `./run.sh` or `bash run.sh`

### Manual Installation
1. **Install Python 3.8+** from [python.org](https://www.python.org/downloads/)
2. **Install dependencies**: `pip install -r requirements.txt`
3. **Run application**: `python app.py`
4. **Open browser**: Navigate to `http://127.0.0.1:5000`

## 📋 System Requirements

- **Python 3.8 or higher**
- **Web browser** (Chrome, Firefox, Safari, Edge)
- **Operating System**: Windows, macOS, or Linux
- **RAM**: 512MB minimum
- **Storage**: 50MB free space

## 🔧 Troubleshooting

### Python Not Found
**Windows:**
- Download Python from [python.org](https://www.python.org/downloads/)
- ✅ Check "Add Python to PATH" during installation
- Restart Command Prompt

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install python3 python3-pip
```

**macOS:**
```bash
# Install Homebrew first: https://brew.sh
brew install python3
```

### Dependencies Installation Fails
```bash
# Update pip first
python -m pip install --upgrade pip

# Then install dependencies
pip install -r requirements.txt
```

### Port 5000 Already in Use
- Close other applications using port 5000
- Or modify `app.py` to use a different port:
  ```python
  app.run(debug=True, host='127.0.0.1', port=5001)
  ```

### Permission Errors
**Windows:**
- Run Command Prompt as Administrator
- Or install to user directory: `pip install --user -r requirements.txt`

**Linux/Mac:**
```bash
# Install to user directory
pip3 install --user -r requirements.txt

# Or use sudo (not recommended)
sudo pip3 install -r requirements.txt
```

## 🧪 Testing Installation

Run the test script to verify everything works:
```bash
python test_app.py
```

Or use the setup script:
```bash
python setup.py
```

## 📁 File Structure

```
activation/
├── 📄 app.py                 # Main Flask application
├── 📄 models.py              # Database models
├── 📄 activation.py          # Trial activation system
├── 📄 requirements.txt       # Python dependencies
├── 📄 run.bat               # Windows launcher
├── 📄 run.sh                # Linux/Mac launcher
├── 📄 setup.py              # Setup script
├── 📄 test_app.py           # Test script
├── 📄 README.md             # Documentation
├── 📄 INSTALLATION.md       # This file
├── 📁 templates/            # HTML templates
│   ├── base.html
│   ├── index.html
│   ├── employees.html
│   ├── employee_form.html
│   ├── children.html
│   └── activation_expired.html
└── 📁 static/               # CSS and JavaScript
    ├── css/style.css
    └── js/main.js
```

## 🌐 Accessing the Application

Once started, the application will be available at:
- **Local access**: http://127.0.0.1:5000
- **Network access**: http://[your-ip]:5000

### Default Pages
- **Dashboard**: http://127.0.0.1:5000/
- **Employees**: http://127.0.0.1:5000/employees
- **Add Employee**: http://127.0.0.1:5000/employee/add

## ⚠️ Trial Version Information

### Trial Limitations
- **Duration**: 3 days from first launch
- **Hardware Lock**: Tied to hard drive serial number
- **Auto-deletion**: Application deletes itself after expiration
- **No recovery**: Data cannot be recovered after deletion

### Trial Status
- Monitor trial status in the navigation bar
- **Green**: More than 1 day remaining
- **Yellow**: 1 day or less remaining  
- **Red**: Trial expired

### What Happens on Expiration
1. Application redirects to expiration page
2. 10-second countdown begins
3. Application files are automatically deleted
4. Browser window closes

## 🔒 Security Features

- Hardware fingerprinting prevents trial extension
- Automatic cleanup ensures no data remnants
- Input validation prevents malicious data entry
- Session management for secure access

## 📞 Support

### Common Issues
1. **Application won't start**: Check Python installation and dependencies
2. **Database errors**: Delete `database.db` and restart
3. **Browser won't open**: Manually navigate to http://127.0.0.1:5000
4. **Trial expired early**: Check system date/time settings

### Getting Help
- Check `README.md` for detailed documentation
- Run `python test_app.py` to diagnose issues
- Ensure all files are present and unmodified

## 🎯 Next Steps

After successful installation:

1. **Start the application** using one of the methods above
2. **Create your first employee** using the "Add Employee" button
3. **Add children** to employees as needed
4. **Monitor trial status** in the navigation bar
5. **Backup important data** before trial expiration

---

**⚠️ Important**: This is a trial version that will automatically delete itself after 3 days. Please backup any important data before the trial expires.
