{% extends "base.html" %}

{% block title %}الرئيسية - نظام إدارة الموظفين{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
        </h1>
    </div>
</div>

<!-- Trial Status Card -->
{% if activation_info %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-{% if activation_info.is_expired %}danger{% elif activation_info.days_remaining <= 1 %}warning{% else %}info{% endif %}">
            <div class="card-header bg-{% if activation_info.is_expired %}danger{% elif activation_info.days_remaining <= 1 %}warning{% else %}info{% endif %} text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt"></i> حالة التجربة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>تاريخ التثبيت:</strong><br>
                        {{ activation_info.install_date.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                    <div class="col-md-3">
                        <strong>تاريخ الانتهاء:</strong><br>
                        {{ activation_info.expiration_date.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                    <div class="col-md-3">
                        <strong>الوقت المتبقي:</strong><br>
                        {% if activation_info.is_expired %}
                            <span class="text-danger">انتهت التجربة</span>
                        {% else %}
                            {{ activation_info.days_remaining }} أيام، {{ activation_info.hours_remaining }} ساعات
                        {% endif %}
                    </div>
                    <div class="col-md-3">
                        <strong>معرف الجهاز:</strong><br>
                        <small class="text-muted">{{ activation_info.hdd_serial[:15] }}...</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ employee_count }}</h4>
                        <p class="card-text">إجمالي الموظفين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-tie fa-3x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('employees') }}" class="text-white text-decoration-none">
                    عرض جميع الموظفين <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ children_count }}</h4>
                        <p class="card-text">إجمالي الأطفال</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-child fa-3x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('employees') }}" class="text-white text-decoration-none">
                    إدارة الأطفال <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt"></i> الإجراءات السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('add_employee') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-user-plus"></i><br>
                            إضافة موظف جديد
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('employees') }}" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-list"></i><br>
                            عرض جميع الموظفين
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <button class="btn btn-secondary btn-lg w-100" onclick="updateTrialStatus()">
                            <i class="fas fa-sync-alt"></i><br>
                            تحديث حالة التجربة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">
                    <i class="fas fa-info-circle"></i> 
                    هذه نسخة تجريبية من نظام إدارة الموظفين. 
                    سيتم حذف جميع البيانات تلقائياً عند انتهاء فترة التجربة.
                </p>
                <p class="text-muted mb-0">
                    <i class="fas fa-database"></i> 
                    قاعدة البيانات: SQLite | 
                    <i class="fas fa-server"></i> 
                    الإطار: Flask | 
                    <i class="fas fa-code"></i> 
                    الإصدار: 1.0.0
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
