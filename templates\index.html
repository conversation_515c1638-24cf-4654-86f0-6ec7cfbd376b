{% extends "base.html" %}

{% block title %}Dashboard - Employee Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt"></i> Dashboard
        </h1>
    </div>
</div>

<!-- Trial Status Card -->
{% if activation_info %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-{% if activation_info.is_expired %}danger{% elif activation_info.days_remaining <= 1 %}warning{% else %}info{% endif %}">
            <div class="card-header bg-{% if activation_info.is_expired %}danger{% elif activation_info.days_remaining <= 1 %}warning{% else %}info{% endif %} text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt"></i> Trial Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>Install Date:</strong><br>
                        {{ activation_info.install_date.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                    <div class="col-md-3">
                        <strong>Expiration Date:</strong><br>
                        {{ activation_info.expiration_date.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                    <div class="col-md-3">
                        <strong>Time Remaining:</strong><br>
                        {% if activation_info.is_expired %}
                            <span class="text-danger">EXPIRED</span>
                        {% else %}
                            {{ activation_info.days_remaining }} days, {{ activation_info.hours_remaining }} hours
                        {% endif %}
                    </div>
                    <div class="col-md-3">
                        <strong>HDD Serial:</strong><br>
                        <small class="text-muted">{{ activation_info.hdd_serial[:15] }}...</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ employee_count }}</h4>
                        <p class="card-text">Total Employees</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-tie fa-3x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('employees') }}" class="text-white text-decoration-none">
                    View All Employees <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ children_count }}</h4>
                        <p class="card-text">Total Children</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-child fa-3x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('employees') }}" class="text-white text-decoration-none">
                    Manage Children <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('add_employee') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-user-plus"></i><br>
                            Add New Employee
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('employees') }}" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-list"></i><br>
                            View All Employees
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <button class="btn btn-secondary btn-lg w-100" onclick="refreshTrialStatus()">
                            <i class="fas fa-sync-alt"></i><br>
                            Refresh Trial Status
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity (placeholder for future enhancement) -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history"></i> System Information
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">
                    <i class="fas fa-info-circle"></i> 
                    This is a trial version of the Employee Management System. 
                    All data will be automatically deleted when the trial expires.
                </p>
                <p class="text-muted mb-0">
                    <i class="fas fa-database"></i> 
                    Database: SQLite | 
                    <i class="fas fa-server"></i> 
                    Framework: Flask | 
                    <i class="fas fa-code"></i> 
                    Version: 1.0.0
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshTrialStatus() {
    fetch('/api/activation-info')
        .then(response => response.json())
        .then(data => {
            if (data) {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error refreshing trial status:', error);
        });
}
</script>
{% endblock %}
