from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from datetime import datetime
import os
import sys

# Import our modules
from models import db, Employee, Child, Activation, init_db
from activation import check_activation, delete_application, get_activation_info

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///database.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize database
init_db(app)

@app.before_request
def check_trial_status():
    """Check activation status before each request"""
    # Skip activation check for static files and activation-related routes
    if request.endpoint and (request.endpoint.startswith('static') or 
                           request.endpoint == 'activation_expired'):
        return
    
    is_valid, message = check_activation()
    
    if not is_valid:
        # Trial has expired
        if request.endpoint != 'activation_expired':
            return redirect(url_for('activation_expired'))

@app.route('/')
def index():
    """Dashboard/Home page"""
    activation_info = get_activation_info()
    employee_count = Employee.query.count()
    children_count = Child.query.count()
    
    return render_template('index.html', 
                         activation_info=activation_info,
                         employee_count=employee_count,
                         children_count=children_count)

@app.route('/employees')
def employees():
    """Employee management page"""
    employees_list = Employee.query.all()
    return render_template('employees.html', employees=employees_list)

@app.route('/employee/add', methods=['GET', 'POST'])
def add_employee():
    """Add new employee"""
    if request.method == 'POST':
        try:
            employee = Employee(
                first_name=request.form['first_name'],
                last_name=request.form['last_name'],
                date_of_birth=datetime.strptime(request.form['date_of_birth'], '%Y-%m-%d').date(),
                place_of_birth=request.form['place_of_birth'],
                position=request.form['position'],
                workplace=request.form['workplace'],
                employment_date=datetime.strptime(request.form['employment_date'], '%Y-%m-%d').date(),
                salary_amount=float(request.form['salary_amount']),
                bank_account_number=request.form['bank_account_number'],
                bank_name=request.form['bank_name'],
                agency=request.form['agency']
            )
            
            db.session.add(employee)
            db.session.commit()
            flash('Employee added successfully!', 'success')
            return redirect(url_for('employees'))
            
        except Exception as e:
            flash(f'Error adding employee: {str(e)}', 'error')
    
    return render_template('employee_form.html', employee=None, action='Add')

@app.route('/employee/edit/<int:employee_id>', methods=['GET', 'POST'])
def edit_employee(employee_id):
    """Edit existing employee"""
    employee = Employee.query.get_or_404(employee_id)
    
    if request.method == 'POST':
        try:
            employee.first_name = request.form['first_name']
            employee.last_name = request.form['last_name']
            employee.date_of_birth = datetime.strptime(request.form['date_of_birth'], '%Y-%m-%d').date()
            employee.place_of_birth = request.form['place_of_birth']
            employee.position = request.form['position']
            employee.workplace = request.form['workplace']
            employee.employment_date = datetime.strptime(request.form['employment_date'], '%Y-%m-%d').date()
            employee.salary_amount = float(request.form['salary_amount'])
            employee.bank_account_number = request.form['bank_account_number']
            employee.bank_name = request.form['bank_name']
            employee.agency = request.form['agency']
            
            db.session.commit()
            flash('Employee updated successfully!', 'success')
            return redirect(url_for('employees'))
            
        except Exception as e:
            flash(f'Error updating employee: {str(e)}', 'error')
    
    return render_template('employee_form.html', employee=employee, action='Edit')

@app.route('/employee/delete/<int:employee_id>', methods=['POST'])
def delete_employee(employee_id):
    """Delete employee"""
    try:
        employee = Employee.query.get_or_404(employee_id)
        db.session.delete(employee)
        db.session.commit()
        flash('Employee deleted successfully!', 'success')
    except Exception as e:
        flash(f'Error deleting employee: {str(e)}', 'error')
    
    return redirect(url_for('employees'))

@app.route('/children/<int:employee_id>')
def children(employee_id):
    """Children management for specific employee"""
    employee = Employee.query.get_or_404(employee_id)
    children_list = Child.query.filter_by(employee_id=employee_id).all()
    return render_template('children.html', employee=employee, children=children_list)

@app.route('/child/add/<int:employee_id>', methods=['POST'])
def add_child(employee_id):
    """Add child to employee"""
    try:
        child = Child(
            employee_id=employee_id,
            name=request.form['name'],
            date_of_birth=datetime.strptime(request.form['date_of_birth'], '%Y-%m-%d').date(),
            educational_level=request.form['educational_level']
        )
        
        db.session.add(child)
        db.session.commit()
        flash('Child added successfully!', 'success')
        
    except Exception as e:
        flash(f'Error adding child: {str(e)}', 'error')
    
    return redirect(url_for('children', employee_id=employee_id))

@app.route('/child/delete/<int:child_id>', methods=['POST'])
def delete_child(child_id):
    """Delete child"""
    try:
        child = Child.query.get_or_404(child_id)
        employee_id = child.employee_id
        db.session.delete(child)
        db.session.commit()
        flash('Child deleted successfully!', 'success')
        return redirect(url_for('children', employee_id=employee_id))
        
    except Exception as e:
        flash(f'Error deleting child: {str(e)}', 'error')
        return redirect(url_for('employees'))

@app.route('/activation-expired')
def activation_expired():
    """Trial expiration page"""
    # Trigger application deletion
    delete_application()
    return render_template('activation_expired.html')

@app.route('/api/activation-info')
def api_activation_info():
    """API endpoint for activation information"""
    activation_info = get_activation_info()
    return jsonify(activation_info)

if __name__ == '__main__':
    # Check activation on startup
    is_valid, message = check_activation()
    
    if not is_valid:
        print(message)
        print("Application will now exit and delete itself.")
        delete_application()
        sys.exit(1)
    
    print(f"Application started successfully! {message}")
    print("=" * 50)
    print("Employee Management System - Trial Version")
    print("=" * 50)
    print("Access the application at: http://127.0.0.1:5000")
    print("Press Ctrl+C to stop the application")
    print("=" * 50)
    
    app.run(debug=True, host='127.0.0.1', port=5000)
