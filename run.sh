#!/bin/bash

echo "========================================"
echo "Employee Management System - Trial Version"
echo "========================================"
echo

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed"
    echo
    echo "Please install Python 3.8 or higher:"
    echo "  Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "  CentOS/RHEL:   sudo yum install python3 python3-pip"
    echo "  macOS:         brew install python3"
    echo
    exit 1
fi

echo "Python 3 found. Checking dependencies..."

# Install dependencies
echo "Installing required packages..."
python3 -m pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo
    echo "ERROR: Failed to install dependencies"
    echo "Please make sure pip is working correctly"
    echo
    exit 1
fi

echo
echo "Starting Employee Management System..."
echo
echo "The application will be available at:"
echo "http://127.0.0.1:5000"
echo
echo "Press Ctrl+C to stop the application"
echo

# Start the Flask application
python3 app.py
