{% extends "base.html" %}

{% block title %}{{ action }} موظف - نظام إدارة الموظفين{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user-{{ 'edit' if employee else 'plus' }}"></i> {{ action }} موظف
            </h1>
            <a href="{{ url_for('employees') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة للموظفين
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-form"></i> معلومات الموظف
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <!-- Personal Information -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user"></i> المعلومات الشخصية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="first_name" class="form-label">الاسم الأول *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="{{ employee.first_name if employee else '' }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="last_name" class="form-label">اسم العائلة *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="{{ employee.last_name if employee else '' }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="date_of_birth" class="form-label">تاريخ الميلاد *</label>
                                <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" 
                                       value="{{ employee.date_of_birth.strftime('%Y-%m-%d') if employee and employee.date_of_birth else '' }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="place_of_birth" class="form-label">مكان الميلاد *</label>
                                <input type="text" class="form-control" id="place_of_birth" name="place_of_birth" 
                                       value="{{ employee.place_of_birth if employee else '' }}" required>
                            </div>
                        </div>
                        
                        <!-- Employment Information -->
                        <div class="col-md-6">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-briefcase"></i> معلومات العمل
                            </h6>
                            
                            <div class="mb-3">
                                <label for="position" class="form-label">المنصب *</label>
                                <input type="text" class="form-control" id="position" name="position" 
                                       value="{{ employee.position if employee else '' }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="workplace" class="form-label">مكان العمل *</label>
                                <input type="text" class="form-control" id="workplace" name="workplace" 
                                       value="{{ employee.workplace if employee else '' }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="employment_date" class="form-label">تاريخ التوظيف *</label>
                                <input type="date" class="form-control" id="employment_date" name="employment_date" 
                                       value="{{ employee.employment_date.strftime('%Y-%m-%d') if employee and employee.employment_date else '' }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="salary_amount" class="form-label">مبلغ الراتب *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="salary_amount" name="salary_amount" 
                                           step="0.01" min="0" value="{{ employee.salary_amount if employee else '' }}" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Banking Information -->
                    <hr>
                    <h6 class="text-info mb-3">
                        <i class="fas fa-university"></i> المعلومات المصرفية
                    </h6>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="bank_account_number" class="form-label">رقم الحساب المصرفي *</label>
                                <input type="text" class="form-control" id="bank_account_number" name="bank_account_number" 
                                       value="{{ employee.bank_account_number if employee else '' }}" required>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="bank_name" class="form-label">اسم البنك *</label>
                                <input type="text" class="form-control" id="bank_name" name="bank_name" 
                                       value="{{ employee.bank_name if employee else '' }}" required>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="agency" class="form-label">الفرع *</label>
                                <input type="text" class="form-control" id="agency" name="agency" 
                                       value="{{ employee.agency if employee else '' }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <hr>
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('employees') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> {{ action }} الموظف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% if employee %}
<div class="row mt-4">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-child"></i> الأطفال ({{ employee.children|length }})
                </h5>
            </div>
            <div class="card-body">
                {% if employee.children %}
                    <div class="list-group">
                        {% for child in employee.children %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ child.name }}</strong><br>
                                <small class="text-muted">
                                    تاريخ الميلاد: {{ child.date_of_birth.strftime('%Y-%m-%d') }} | 
                                    المستوى التعليمي: {{ child.educational_level }}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا يوجد أطفال مسجلين لهذا الموظف.</p>
                {% endif %}
                
                <div class="text-center mt-3">
                    <a href="{{ url_for('children', employee_id=employee.id) }}" class="btn btn-info">
                        <i class="fas fa-child"></i> إدارة الأطفال
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
