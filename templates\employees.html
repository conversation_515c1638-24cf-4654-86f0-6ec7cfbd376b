{% extends "base.html" %}

{% block title %}الموظفين - نظام إدارة الموظفين{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user-tie"></i> الموظفين
            </h1>
            <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة موظف
            </a>
        </div>
    </div>
</div>

{% if employees %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> قائمة الموظفين ({{ employees|length }} موظف)
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>الرقم</th>
                                <th>الاسم</th>
                                <th>المنصب</th>
                                <th>مكان العمل</th>
                                <th>تاريخ التوظيف</th>
                                <th>الراتب</th>
                                <th>الأطفال</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees %}
                            <tr>
                                <td>{{ employee.id }}</td>
                                <td>
                                    <strong>{{ employee.first_name }} {{ employee.last_name }}</strong><br>
                                    <small class="text-muted">{{ employee.place_of_birth }}</small>
                                </td>
                                <td>{{ employee.position }}</td>
                                <td>{{ employee.workplace }}</td>
                                <td>{{ employee.employment_date.strftime('%Y-%m-%d') }}</td>
                                <td>${{ "%.2f"|format(employee.salary_amount) }}</td>
                                <td>
                                    <a href="{{ url_for('children', employee_id=employee.id) }}" class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-child"></i> {{ employee.children|length }}
                                    </a>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('edit_employee', employee_id=employee.id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('children', employee_id=employee.id) }}" 
                                           class="btn btn-sm btn-outline-info" title="إدارة الأطفال">
                                            <i class="fas fa-child"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete({{ employee.id }}, '{{ employee.first_name }} {{ employee.last_name }}')"
                                                title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-user-slash fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا يوجد موظفين</h4>
                <p class="text-muted">ابدأ بإضافة أول موظف.</p>
                <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة أول موظف
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف <strong id="employeeName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع سجلات الأطفال المرتبطة أيضاً.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(employeeId, employeeName) {
    document.getElementById('employeeName').textContent = employeeName;
    document.getElementById('deleteForm').action = '/employee/delete/' + employeeId;
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
{% endblock %}
