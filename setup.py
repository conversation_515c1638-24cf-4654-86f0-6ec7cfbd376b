#!/usr/bin/env python3
"""
Setup script for Employee Management System
This script helps users set up and run the application
"""

import os
import sys
import subprocess
import platform

def print_header():
    """Print application header"""
    print("=" * 60)
    print("Employee Management System - Setup & Installation")
    print("=" * 60)
    print()

def check_python():
    """Check Python version"""
    print("Checking Python installation...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} detected")
        print("❌ Python 3.8 or higher is required")
        print()
        print("Please install Python 3.8+ from: https://www.python.org/downloads/")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_pip():
    """Check if pip is available"""
    print("Checking pip installation...")
    
    try:
        import pip
        print("✅ pip is available")
        return True
    except ImportError:
        print("❌ pip is not available")
        print("Please install pip or reinstall Python with pip included")
        return False

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    
    try:
        # Read requirements
        if not os.path.exists('requirements.txt'):
            print("❌ requirements.txt not found")
            return False
        
        # Install using pip
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print("❌ Failed to install dependencies")
            print("Error:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def check_files():
    """Check if all required files exist"""
    print("Checking application files...")
    
    required_files = [
        'app.py',
        'models.py',
        'activation.py',
        'requirements.txt',
        'templates/base.html',
        'templates/index.html',
        'templates/employees.html',
        'templates/employee_form.html',
        'templates/children.html',
        'templates/activation_expired.html',
        'static/css/style.css',
        'static/js/main.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ All required files present")
    return True

def test_application():
    """Test if the application can start"""
    print("Testing application...")
    
    try:
        # Import main modules
        from models import Employee, Child, Activation
        from activation import get_hdd_serial
        
        print("✅ Core modules imported successfully")
        
        # Test HDD serial retrieval
        serial = get_hdd_serial()
        print(f"✅ Hardware detection working (Serial: {serial[:10]}...)")
        
        return True
        
    except Exception as e:
        print(f"❌ Application test failed: {e}")
        return False

def create_desktop_shortcut():
    """Create desktop shortcut (Windows only)"""
    if platform.system() != "Windows":
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "Employee Management System.lnk")
        target = os.path.join(os.getcwd(), "run.bat")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✅ Desktop shortcut created")
        
    except ImportError:
        print("ℹ️  Desktop shortcut creation skipped (winshell not available)")
    except Exception as e:
        print(f"⚠️  Could not create desktop shortcut: {e}")

def print_instructions():
    """Print usage instructions"""
    print()
    print("=" * 60)
    print("🎉 Setup completed successfully!")
    print("=" * 60)
    print()
    print("To start the Employee Management System:")
    print()
    
    if platform.system() == "Windows":
        print("Option 1 (Recommended):")
        print("   Double-click 'run.bat' in this folder")
        print()
        print("Option 2:")
        print("   Open Command Prompt in this folder and run:")
        print("   python app.py")
    else:
        print("Open terminal in this folder and run:")
        print("   python3 app.py")
    
    print()
    print("The application will:")
    print("   ✓ Start the web server")
    print("   ✓ Open your default web browser")
    print("   ✓ Navigate to http://127.0.0.1:5000")
    print()
    print("⚠️  TRIAL VERSION NOTICE:")
    print("   • This is a 3-day trial version")
    print("   • Trial starts on first launch")
    print("   • Application will auto-delete after trial expires")
    print("   • Trial is tied to your hardware")
    print()
    print("📖 For more information, see README.md")
    print("=" * 60)

def main():
    """Main setup function"""
    print_header()
    
    # Check system requirements
    if not check_python():
        return False
    
    if not check_pip():
        return False
    
    # Check files
    if not check_files():
        return False
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Test application
    if not test_application():
        return False
    
    # Create shortcuts (optional)
    create_desktop_shortcut()
    
    # Print instructions
    print_instructions()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print()
            print("❌ Setup failed. Please check the errors above.")
            input("Press Enter to exit...")
        else:
            print()
            input("Press Enter to exit...")
    except KeyboardInterrupt:
        print("\n\n❌ Setup cancelled by user")
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        input("Press Enter to exit...")
