import os
import sys
import shutil
import psutil
import platform
from datetime import datetime, timedelta
from models import db, Activation

def get_hdd_serial():
    """Get the hard drive serial number"""
    try:
        if platform.system() == "Windows":
            # For Windows
            import subprocess
            result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber'], 
                                  capture_output=True, text=True)
            lines = result.stdout.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line and line != 'SerialNumber':
                    return line
        else:
            # For Linux/Mac - get the first disk serial
            partitions = psutil.disk_partitions()
            if partitions:
                # Try to get serial from /proc/scsi/scsi or similar
                try:
                    with open('/proc/scsi/scsi', 'r') as f:
                        content = f.read()
                        # Extract serial from SCSI info (simplified)
                        return "LINUX_" + str(hash(content))[:10]
                except:
                    pass
            
        # Fallback: use a combination of system info
        import uuid
        mac = uuid.getnode()
        return f"FALLBACK_{mac}"
        
    except Exception as e:
        print(f"Error getting HDD serial: {e}")
        # Ultimate fallback
        import uuid
        return f"ERROR_{uuid.uuid4().hex[:10]}"

def check_activation():
    """Check if the application is still within the trial period"""
    try:
        current_hdd_serial = get_hdd_serial()
        
        # Check if activation record exists
        activation = Activation.query.filter_by(hdd_serial=current_hdd_serial).first()
        
        if not activation:
            # First run - create activation record
            install_date = datetime.now()
            expiration_date = install_date + timedelta(days=3)
            
            new_activation = Activation(
                install_date=install_date,
                hdd_serial=current_hdd_serial,
                expiration_date=expiration_date
            )
            
            db.session.add(new_activation)
            db.session.commit()
            
            print(f"Trial activated! Expires on: {expiration_date}")
            return True, f"Trial period started. Expires on: {expiration_date.strftime('%Y-%m-%d %H:%M:%S')}"
        
        # Check if trial has expired
        current_time = datetime.now()
        if current_time > activation.expiration_date:
            return False, "Trial period has expired!"
        
        # Trial is still valid
        remaining_time = activation.expiration_date - current_time
        return True, f"Trial expires in: {remaining_time.days} days, {remaining_time.seconds // 3600} hours"
        
    except Exception as e:
        print(f"Error checking activation: {e}")
        return False, f"Activation check failed: {str(e)}"

def delete_application():
    """Delete the application files after trial expiration"""
    try:
        app_directory = os.path.dirname(os.path.abspath(__file__))
        
        print(f"Trial expired! Deleting application from: {app_directory}")
        
        # Create a batch file to delete the directory after the Python process exits
        if platform.system() == "Windows":
            batch_content = f'''@echo off
timeout /t 2 /nobreak > nul
rmdir /s /q "{app_directory}"
del "%~f0"
'''
            batch_file = os.path.join(os.path.dirname(app_directory), "cleanup.bat")
            with open(batch_file, 'w') as f:
                f.write(batch_content)
            
            # Execute the batch file
            import subprocess
            subprocess.Popen([batch_file], shell=True)
        else:
            # For Linux/Mac
            script_content = f'''#!/bin/bash
sleep 2
rm -rf "{app_directory}"
rm -- "$0"
'''
            script_file = os.path.join(os.path.dirname(app_directory), "cleanup.sh")
            with open(script_file, 'w') as f:
                f.write(script_content)
            
            os.chmod(script_file, 0o755)
            import subprocess
            subprocess.Popen([script_file])
        
        return True
        
    except Exception as e:
        print(f"Error deleting application: {e}")
        return False

def get_activation_info():
    """Get current activation information"""
    try:
        current_hdd_serial = get_hdd_serial()
        activation = Activation.query.filter_by(hdd_serial=current_hdd_serial).first()
        
        if activation:
            current_time = datetime.now()
            remaining_time = activation.expiration_date - current_time
            
            return {
                'install_date': activation.install_date,
                'expiration_date': activation.expiration_date,
                'hdd_serial': activation.hdd_serial,
                'days_remaining': max(0, remaining_time.days),
                'hours_remaining': max(0, remaining_time.seconds // 3600),
                'is_expired': current_time > activation.expiration_date
            }
        
        return None
        
    except Exception as e:
        print(f"Error getting activation info: {e}")
        return None
