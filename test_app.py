#!/usr/bin/env python3
"""
Test script for Employee Management System
This script tests the basic functionality without running the full web server
"""

import os
import sys
import tempfile
from datetime import datetime, timedelta

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import flask
        print("✓ Flask imported successfully")
    except ImportError as e:
        print(f"✗ Flask import failed: {e}")
        return False
    
    try:
        import sqlalchemy
        print("✓ SQLAlchemy imported successfully")
    except ImportError as e:
        print(f"✗ SQLAlchemy import failed: {e}")
        return False
    
    try:
        import psutil
        print("✓ psutil imported successfully")
    except ImportError as e:
        print(f"✗ psutil import failed: {e}")
        return False
    
    return True

def test_models():
    """Test database models"""
    print("\nTesting database models...")
    
    try:
        from models import Employee, Child, Activation, db
        print("✓ Models imported successfully")
        
        # Test model creation (without database)
        employee = Employee(
            first_name="<PERSON>",
            last_name="Doe",
            date_of_birth=datetime(1990, 1, 1).date(),
            place_of_birth="New York",
            position="Developer",
            workplace="Tech Corp",
            employment_date=datetime(2020, 1, 1).date(),
            salary_amount=50000.0,
            bank_account_number="*********",
            bank_name="Test Bank",
            agency="Main Branch"
        )
        print("✓ Employee model creation successful")
        
        child = Child(
            employee_id=1,
            name="Jane Doe",
            date_of_birth=datetime(2010, 1, 1).date(),
            educational_level="Elementary"
        )
        print("✓ Child model creation successful")
        
        activation = Activation(
            install_date=datetime.now(),
            hdd_serial="TEST123",
            expiration_date=datetime.now() + timedelta(days=3)
        )
        print("✓ Activation model creation successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Model test failed: {e}")
        return False

def test_activation():
    """Test activation system"""
    print("\nTesting activation system...")
    
    try:
        from activation import get_hdd_serial
        
        serial = get_hdd_serial()
        print(f"✓ HDD serial retrieved: {serial[:10]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ Activation test failed: {e}")
        return False

def test_flask_app():
    """Test Flask app creation"""
    print("\nTesting Flask application...")
    
    try:
        # Create a temporary database for testing
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            temp_db_path = tmp_db.name
        
        # Import and configure app
        from app import app
        app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{temp_db_path}'
        app.config['TESTING'] = True
        
        # Test app creation
        with app.app_context():
            from models import db
            db.create_all()
            print("✓ Database tables created successfully")
        
        # Test client creation
        client = app.test_client()
        print("✓ Test client created successfully")
        
        # Clean up
        os.unlink(temp_db_path)
        
        return True
        
    except Exception as e:
        print(f"✗ Flask app test failed: {e}")
        return False

def test_templates():
    """Test if template files exist"""
    print("\nTesting template files...")
    
    templates = [
        'templates/base.html',
        'templates/index.html',
        'templates/employees.html',
        'templates/employee_form.html',
        'templates/children.html',
        'templates/activation_expired.html'
    ]
    
    all_exist = True
    for template in templates:
        if os.path.exists(template):
            print(f"✓ {template} exists")
        else:
            print(f"✗ {template} missing")
            all_exist = False
    
    return all_exist

def test_static_files():
    """Test if static files exist"""
    print("\nTesting static files...")
    
    static_files = [
        'static/css/style.css',
        'static/js/main.js'
    ]
    
    all_exist = True
    for static_file in static_files:
        if os.path.exists(static_file):
            print(f"✓ {static_file} exists")
        else:
            print(f"✗ {static_file} missing")
            all_exist = False
    
    return all_exist

def main():
    """Run all tests"""
    print("=" * 50)
    print("Employee Management System - Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_models,
        test_activation,
        test_flask_app,
        test_templates,
        test_static_files
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application should work correctly.")
        print("\nTo start the application:")
        print("1. Run: python app.py")
        print("2. Open: http://127.0.0.1:5000")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\nMake sure you have installed all dependencies:")
        print("pip install -r requirements.txt")
    
    print("=" * 50)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
