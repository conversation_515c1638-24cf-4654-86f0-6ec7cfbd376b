// Main JavaScript for Employee Management System

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the application
    initializeApp();
    
    // Update trial status periodically
    updateTrialStatus();
    setInterval(updateTrialStatus, 60000); // Update every minute
    
    // Add fade-in animation to cards
    addFadeInAnimation();
    
    // Initialize tooltips
    initializeTooltips();
    
    // Form validation
    initializeFormValidation();
});

/**
 * Initialize the application
 */
function initializeApp() {
    console.log('Employee Management System initialized');
    
    // Add loading states to buttons
    addButtonLoadingStates();
    
    // Initialize data tables if present
    initializeDataTables();
    
    // Add confirmation dialogs
    addConfirmationDialogs();
}

/**
 * Update trial status in the navigation
 */
function updateTrialStatus() {
    const trialStatusElement = document.getElementById('trial-status');
    
    if (!trialStatusElement) return;
    
    fetch('/api/activation-info')
        .then(response => response.json())
        .then(data => {
            if (data) {
                const daysRemaining = data.days_remaining;
                const hoursRemaining = data.hours_remaining;
                const isExpired = data.is_expired;
                
                let statusText = '';
                let statusClass = '';
                
                if (isExpired) {
                    statusText = '<i class="fas fa-exclamation-triangle"></i> EXPIRED';
                    statusClass = 'trial-status danger text-danger';
                } else if (daysRemaining === 0) {
                    statusText = `<i class="fas fa-clock"></i> ${hoursRemaining}h remaining`;
                    statusClass = 'trial-status danger text-warning';
                } else if (daysRemaining <= 1) {
                    statusText = `<i class="fas fa-clock"></i> ${daysRemaining}d ${hoursRemaining}h remaining`;
                    statusClass = 'trial-status warning text-warning';
                } else {
                    statusText = `<i class="fas fa-clock"></i> ${daysRemaining} days remaining`;
                    statusClass = 'trial-status text-light';
                }
                
                trialStatusElement.innerHTML = statusText;
                trialStatusElement.className = statusClass;
                
                // Redirect if expired
                if (isExpired) {
                    setTimeout(() => {
                        window.location.href = '/activation-expired';
                    }, 2000);
                }
            }
        })
        .catch(error => {
            console.error('Error updating trial status:', error);
            trialStatusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Status Error';
            trialStatusElement.className = 'trial-status text-danger';
        });
}

/**
 * Add fade-in animation to cards
 */
function addFadeInAnimation() {
    const cards = document.querySelectorAll('.card');
    
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Add loading states to buttons
 */
function addButtonLoadingStates() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitButton = form.querySelector('button[type="submit"]');
            
            if (submitButton) {
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<span class="loading"></span> Processing...';
                submitButton.disabled = true;
                
                // Re-enable button after 5 seconds (fallback)
                setTimeout(() => {
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                }, 5000);
            }
        });
    });
}

/**
 * Initialize data tables
 */
function initializeDataTables() {
    const tables = document.querySelectorAll('.table');
    
    tables.forEach(table => {
        // Add search functionality
        addTableSearch(table);
        
        // Add sorting functionality
        addTableSorting(table);
    });
}

/**
 * Add search functionality to tables
 */
function addTableSearch(table) {
    const tableContainer = table.closest('.card-body');
    
    if (!tableContainer || tableContainer.querySelector('.table-search')) return;
    
    const searchContainer = document.createElement('div');
    searchContainer.className = 'mb-3 table-search';
    searchContainer.innerHTML = `
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" class="form-control" placeholder="Search table...">
        </div>
    `;
    
    tableContainer.insertBefore(searchContainer, table.parentElement);
    
    const searchInput = searchContainer.querySelector('input');
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
}

/**
 * Add sorting functionality to tables
 */
function addTableSorting(table) {
    const headers = table.querySelectorAll('thead th');
    
    headers.forEach((header, index) => {
        if (header.textContent.trim() === 'Actions') return;
        
        header.style.cursor = 'pointer';
        header.innerHTML += ' <i class="fas fa-sort text-muted"></i>';
        
        header.addEventListener('click', function() {
            sortTable(table, index);
        });
    });
}

/**
 * Sort table by column
 */
function sortTable(table, columnIndex) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    const isAscending = table.dataset.sortDirection !== 'asc';
    table.dataset.sortDirection = isAscending ? 'asc' : 'desc';
    
    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();
        
        // Try to parse as numbers
        const aNum = parseFloat(aText.replace(/[^0-9.-]/g, ''));
        const bNum = parseFloat(bText.replace(/[^0-9.-]/g, ''));
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        }
        
        // Sort as strings
        return isAscending ? aText.localeCompare(bText) : bText.localeCompare(aText);
    });
    
    // Update sort icons
    const headers = table.querySelectorAll('thead th i.fa-sort, thead th i.fa-sort-up, thead th i.fa-sort-down');
    headers.forEach(icon => {
        icon.className = 'fas fa-sort text-muted';
    });
    
    const currentHeader = table.querySelectorAll('thead th')[columnIndex];
    const currentIcon = currentHeader.querySelector('i');
    currentIcon.className = `fas fa-sort-${isAscending ? 'up' : 'down'} text-primary`;
    
    // Reorder rows
    rows.forEach(row => tbody.appendChild(row));
}

/**
 * Add confirmation dialogs
 */
function addConfirmationDialogs() {
    const deleteButtons = document.querySelectorAll('[onclick*="confirm"]');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const confirmMessage = 'Are you sure you want to delete this item?';
            
            if (confirm(confirmMessage)) {
                // Execute the original onclick function
                const onclickAttr = this.getAttribute('onclick');
                if (onclickAttr) {
                    eval(onclickAttr);
                }
            }
        });
    });
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    validateField(this);
                }
            });
        });
        
        form.addEventListener('submit', function(e) {
            let isValid = true;
            
            inputs.forEach(input => {
                if (!validateField(input)) {
                    isValid = false;
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showAlert('Please correct the errors in the form.', 'danger');
            }
        });
    });
}

/**
 * Validate individual form field
 */
function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'This field is required.';
    }
    
    // Email validation
    if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid email address.';
        }
    }
    
    // Date validation
    if (field.type === 'date' && value) {
        const date = new Date(value);
        const today = new Date();
        
        if (field.name === 'date_of_birth' && date > today) {
            isValid = false;
            errorMessage = 'Date of birth cannot be in the future.';
        }
    }
    
    // Number validation
    if (field.type === 'number' && value) {
        const num = parseFloat(value);
        if (isNaN(num) || num < 0) {
            isValid = false;
            errorMessage = 'Please enter a valid positive number.';
        }
    }
    
    // Update field appearance
    if (isValid) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
    } else {
        field.classList.remove('is-valid');
        field.classList.add('is-invalid');
        
        // Show error message
        let feedback = field.parentElement.querySelector('.invalid-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            field.parentElement.appendChild(feedback);
        }
        feedback.textContent = errorMessage;
    }
    
    return isValid;
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.querySelector('.container');
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.insertBefore(alert, alertContainer.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 5000);
}

/**
 * Utility function to format currency
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

/**
 * Utility function to format date
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

/**
 * Export data to CSV
 */
function exportToCSV(tableId, filename) {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    const rows = table.querySelectorAll('tr');
    const csvContent = [];
    
    rows.forEach(row => {
        const cols = row.querySelectorAll('td, th');
        const rowData = [];
        
        cols.forEach(col => {
            // Skip action columns
            if (!col.textContent.includes('Actions')) {
                rowData.push('"' + col.textContent.trim().replace(/"/g, '""') + '"');
            }
        });
        
        if (rowData.length > 0) {
            csvContent.push(rowData.join(','));
        }
    });
    
    const blob = new Blob([csvContent.join('\n')], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename || 'export.csv';
    a.click();
    
    window.URL.revokeObjectURL(url);
}
