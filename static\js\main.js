// Main JavaScript for Employee Management System - Arabic RTL

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the application
    initializeApp();
    
    // Update trial status periodically
    updateTrialStatus();
    setInterval(updateTrialStatus, 60000); // Update every minute
    
    // Add fade-in animation to cards
    addFadeInAnimation();
    
    // Initialize form validation
    initializeFormValidation();
});

/**
 * Initialize the application
 */
function initializeApp() {
    console.log('نظام إدارة الموظفين تم تهيئته');
    
    // Add loading states to buttons
    addButtonLoadingStates();
    
    // Initialize data tables if present
    initializeDataTables();
}

/**
 * Update trial status in the navigation
 */
function updateTrialStatus() {
    const trialStatusElement = document.getElementById('trial-status');
    
    if (!trialStatusElement) return;
    
    fetch('/api/activation-info')
        .then(response => response.json())
        .then(data => {
            if (data) {
                const daysRemaining = data.days_remaining;
                const hoursRemaining = data.hours_remaining;
                const isExpired = data.is_expired;
                
                let statusText = '';
                let statusClass = '';
                
                if (isExpired) {
                    statusText = '<i class="fas fa-exclamation-triangle"></i> انتهت التجربة';
                    statusClass = 'trial-status text-danger trial-warning';
                } else if (daysRemaining === 0) {
                    statusText = `<i class="fas fa-clock"></i> ${hoursRemaining} ساعة متبقية`;
                    statusClass = 'trial-status text-warning trial-warning';
                } else if (daysRemaining <= 1) {
                    statusText = `<i class="fas fa-clock"></i> ${daysRemaining} يوم ${hoursRemaining} ساعة متبقية`;
                    statusClass = 'trial-status text-warning';
                } else {
                    statusText = `<i class="fas fa-clock"></i> ${daysRemaining} أيام متبقية`;
                    statusClass = 'trial-status text-light';
                }
                
                trialStatusElement.innerHTML = statusText;
                trialStatusElement.className = statusClass;
                
                // Redirect if expired
                if (isExpired) {
                    setTimeout(() => {
                        window.location.href = '/activation-expired';
                    }, 2000);
                }
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث حالة التجربة:', error);
            trialStatusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i> خطأ في الحالة';
            trialStatusElement.className = 'trial-status text-danger';
        });
}

/**
 * Add fade-in animation to cards
 */
function addFadeInAnimation() {
    const cards = document.querySelectorAll('.card');
    
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

/**
 * Add loading states to buttons
 */
function addButtonLoadingStates() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitButton = form.querySelector('button[type="submit"]');
            
            if (submitButton) {
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<span class="loading"></span> جاري المعالجة...';
                submitButton.disabled = true;
                
                // Re-enable button after 5 seconds (fallback)
                setTimeout(() => {
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                }, 5000);
            }
        });
    });
}

/**
 * Initialize data tables
 */
function initializeDataTables() {
    const tables = document.querySelectorAll('.table');
    
    tables.forEach(table => {
        // Add search functionality
        addTableSearch(table);
    });
}

/**
 * Add search functionality to tables
 */
function addTableSearch(table) {
    const tableContainer = table.closest('.card-body');
    
    if (!tableContainer || tableContainer.querySelector('.table-search')) return;
    
    const searchContainer = document.createElement('div');
    searchContainer.className = 'mb-3 table-search';
    searchContainer.innerHTML = `
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" class="form-control" placeholder="البحث في الجدول...">
        </div>
    `;
    
    tableContainer.insertBefore(searchContainer, table.parentElement);
    
    const searchInput = searchContainer.querySelector('input');
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    validateField(this);
                }
            });
        });
        
        form.addEventListener('submit', function(e) {
            let isValid = true;
            
            inputs.forEach(input => {
                if (!validateField(input)) {
                    isValid = false;
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showAlert('يرجى تصحيح الأخطاء في النموذج.', 'danger');
            }
        });
    });
}

/**
 * Validate individual form field
 */
function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'هذا الحقل مطلوب.';
    }
    
    // Email validation
    if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            errorMessage = 'يرجى إدخال عنوان بريد إلكتروني صحيح.';
        }
    }
    
    // Date validation
    if (field.type === 'date' && value) {
        const date = new Date(value);
        const today = new Date();
        
        if (field.name === 'date_of_birth' && date > today) {
            isValid = false;
            errorMessage = 'تاريخ الميلاد لا يمكن أن يكون في المستقبل.';
        }
    }
    
    // Number validation
    if (field.type === 'number' && value) {
        const num = parseFloat(value);
        if (isNaN(num) || num < 0) {
            isValid = false;
            errorMessage = 'يرجى إدخال رقم صحيح موجب.';
        }
    }
    
    // Update field appearance
    if (isValid) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
    } else {
        field.classList.remove('is-valid');
        field.classList.add('is-invalid');
        
        // Show error message
        let feedback = field.parentElement.querySelector('.invalid-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            field.parentElement.appendChild(feedback);
        }
        feedback.textContent = errorMessage;
    }
    
    return isValid;
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.querySelector('.container');
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.insertBefore(alert, alertContainer.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 5000);
}

/**
 * Utility function to format currency
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

/**
 * Utility function to format date
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}
