# نظام إدارة الموظفين - النسخة التجريبية
# Employee Management System - Trial Version

نظام شامل لإدارة الموظفين عبر الويب مع آلية تفعيل تجريبية لمدة 3 أيام.

## 🚀 الميزات الرئيسية

### الوظائف الأساسية
- **إدارة الموظفين**: إضافة، تعديل، حذف، وعرض سجلات الموظفين
- **إدارة الأطفال**: إدارة سجلات الأطفال المرتبطة بالموظفين
- **واجهة ويب حديثة**: تصميم متجاوب باستخدام Bootstrap 5
- **قاعدة البيانات**: قاعدة بيانات SQLite لتخزين البيانات

### معلومات الموظف
- البيانات الشخصية (الاسم، تاريخ الميلاد، مكان الميلاد)
- معلومات العمل (المنصب، مكان العمل، تاريخ التوظيف، الراتب)
- التفاصيل المصرفية (رقم الحساب، اسم البنك، الفرع)

### معلومات الأطفال
- الاسم وتاريخ الميلاد
- المستوى التعليمي
- مرتبط بالموظف الوالد

### نظام التفعيل التجريبي
- **فترة تجريبية 3 أيام** من أول تشغيل
- **تفعيل مرتبط بالجهاز** باستخدام الرقم التسلسلي للقرص الصلب
- **انتهاء تلقائي** مع حذف التطبيق
- **عرض حالة التجربة** في الوقت الفعلي في شريط التنقل

## 📋 المتطلبات

- **Python 3.8 أو أحدث**
- **متصفح ويب** (Chrome, Firefox, Safari, Edge)
- **Windows, macOS, أو Linux**

## 🛠️ التثبيت والإعداد

### الطريقة الأولى: البدء السريع (Windows)
1. انقر نقراً مزدوجاً على `run_app.bat`
2. سيقوم السكريبت تلقائياً بـ:
   - فحص تثبيت Python
   - تثبيت المتطلبات المطلوبة
   - بدء التطبيق
   - فتح متصفح الويب

### الطريقة الثانية: الإعداد اليدوي
1. **تثبيت Python** (إذا لم يكن مثبتاً):
   - حمل من [python.org](https://www.python.org/downloads/)
   - تأكد من تحديد "Add Python to PATH" أثناء التثبيت

2. **تثبيت المتطلبات**:
   ```bash
   pip install -r requirements.txt
   ```

3. **تشغيل التطبيق**:
   ```bash
   python app.py
   ```

4. **فتح في المتصفح**:
   - انتقل إلى `http://127.0.0.1:5000`

## 🎯 الاستخدام

### التشغيل الأول
- عند التشغيل الأول، سيتم تفعيل فترة التجربة لمدة 3 أيام تلقائياً
- فترة التجربة مرتبطة بالرقم التسلسلي للقرص الصلب لجهازك
- حالة التجربة معروضة في شريط التنقل

### إدارة الموظفين
1. **إضافة موظف**: انقر "إضافة موظف" واملأ المعلومات المطلوبة
2. **تعديل موظف**: انقر زر التعديل (أيقونة القلم) بجانب أي موظف
3. **حذف موظف**: انقر زر الحذف (أيقونة سلة المهملات) وأكد الحذف
4. **عرض التفاصيل**: انقر على اسم الموظف لعرض التفاصيل الكاملة

### إدارة الأطفال
1. انتقل إلى سجل الموظف
2. انقر "إدارة الأطفال" أو شارة عدد الأطفال
3. أضف الأطفال باستخدام النموذج في الأعلى
4. احذف الأطفال باستخدام زر الحذف

### حالة التجربة
- **أخضر**: أكثر من يوم واحد متبقي
- **أصفر**: يوم واحد أو أقل متبقي
- **أحمر**: انتهت التجربة

## 📁 هيكل الملفات

```
activation/
├── app.py                 # تطبيق Flask الرئيسي
├── models.py              # نماذج قاعدة البيانات
├── activation.py          # نظام التفعيل التجريبي
├── requirements.txt       # متطلبات Python
├── run_app.bat           # سكريبت تشغيل Windows
├── README.md             # هذا الملف
├── templates/            # قوالب HTML
│   ├── base.html
│   ├── index.html
│   ├── employees.html
│   ├── employee_form.html
│   ├── children.html
│   └── activation_expired.html
└── static/               # CSS و JavaScript
    ├── css/
    │   └── style.css
    └── js/
        └── main.js
```

## 🔒 ميزات الأمان

### حماية التجربة
- بصمة الجهاز باستخدام الرقم التسلسلي للقرص الصلب
- حذف تلقائي للتطبيق بعد انتهاء التجربة
- مراقبة حالة التفعيل في الوقت الفعلي
- آلية تجربة مقاومة للتلاعب

### أمان البيانات
- قاعدة بيانات SQLite مع علاقات صحيحة
- التحقق من صحة المدخلات وتنظيفها
- حماية CSRF (مدمجة في Flask)
- إدارة جلسات آمنة

## 🎨 واجهة المستخدم

### التصميم الحديث
- **Bootstrap 5** للتخطيط المتجاوب
- **Font Awesome** للأيقونات الواضحة
- **خلفيات متدرجة** ورسوم متحركة سلسة
- **تصميم متجاوب** يعمل على جميع الأجهزة

### الصفحات الرئيسية
- **لوحة التحكم**: نظرة عامة مع الإحصائيات وحالة التجربة
- **قائمة الموظفين**: جدول قابل للبحث والترتيب
- **نموذج الموظف**: إدخال بيانات شامل
- **إدارة الأطفال**: سجلات الأطفال المرتبطة
- **انتهاء التجربة**: إشعار تنظيف تلقائي

## ⚙️ التفاصيل التقنية

### مخطط قاعدة البيانات
```sql
-- جدول الموظفين
CREATE TABLE employees (
    id INTEGER PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    place_of_birth VARCHAR(200) NOT NULL,
    position VARCHAR(100) NOT NULL,
    workplace VARCHAR(200) NOT NULL,
    employment_date DATE NOT NULL,
    salary_amount FLOAT NOT NULL,
    bank_account_number VARCHAR(50) NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    agency VARCHAR(100) NOT NULL
);

-- جدول الأطفال
CREATE TABLE children (
    id INTEGER PRIMARY KEY,
    employee_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    educational_level VARCHAR(100) NOT NULL,
    FOREIGN KEY (employee_id) REFERENCES employees (id)
);

-- جدول التفعيل
CREATE TABLE activation (
    id INTEGER PRIMARY KEY,
    install_date DATETIME NOT NULL,
    hdd_serial VARCHAR(100) NOT NULL,
    expiration_date DATETIME NOT NULL
);
```

### نقاط النهاية API
- `GET /` - لوحة التحكم
- `GET /employees` - قائمة الموظفين
- `GET/POST /employee/add` - إضافة موظف
- `GET/POST /employee/edit/<id>` - تعديل موظف
- `POST /employee/delete/<id>` - حذف موظف
- `GET /children/<employee_id>` - إدارة الأطفال
- `POST /child/add/<employee_id>` - إضافة طفل
- `POST /child/delete/<id>` - حذف طفل
- `GET /api/activation-info` - API حالة التجربة

## 🚨 انتهاء التجربة

عند انتهاء فترة التجربة لمدة 3 أيام:
1. **الكشف التلقائي**: يتحقق التطبيق من حالة التجربة في كل طلب
2. **صفحة الانتهاء**: يتم توجيه المستخدم إلى صفحة العد التنازلي
3. **حذف الملفات**: يتم حذف ملفات التطبيق تلقائياً
4. **الخروج النظيف**: تُغلق نافذة المتصفح تلقائياً

## 🔧 استكشاف الأخطاء وإصلاحها

### المشاكل الشائعة

**Python غير موجود**:
- ثبت Python من python.org
- تأكد من تحديد "Add to PATH" أثناء التثبيت
- أعد تشغيل موجه الأوامر/الطرفية

**فشل تثبيت المتطلبات**:
- حدث pip: `python -m pip install --upgrade pip`
- جرب: `python -m pip install -r requirements.txt`
- تحقق من إعدادات جدار الحماية

**التطبيق لا يبدأ**:
- تحقق من توفر المنفذ 5000
- جرب التشغيل بـ: `python app.py`
- تحقق من إعدادات جدار الحماية

**أخطاء قاعدة البيانات**:
- احذف ملف `database.db` وأعد التشغيل
- تحقق من أذونات الملفات في مجلد التطبيق

## 📝 الترخيص

هذه نسخة تجريبية مع القيود التالية:
- **حد استخدام 3 أيام**
- **حذف تلقائي** بعد انتهاء التجربة
- **لا ضمان أو دعم** مقدم
- **لأغراض التقييم فقط**

---

**⚠️ مهم**: هذه نسخة تجريبية ستحذف نفسها تلقائياً بعد 3 أيام. يرجى نسخ احتياطي لأي بيانات مهمة قبل انتهاء التجربة.
