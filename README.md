# Employee Management System - Trial Version

A comprehensive web-based employee management system with a 3-day trial activation mechanism.

## 🚀 Features

### Core Functionality
- **Employee Management**: Add, edit, delete, and view employee records
- **Children Management**: Manage children records linked to employees
- **Modern Web Interface**: Responsive design using Bootstrap 5
- **Database**: SQLite database for data storage

### Employee Information
- Personal details (name, date of birth, place of birth)
- Employment information (position, workplace, employment date, salary)
- Banking details (account number, bank name, agency)

### Children Information
- Name and date of birth
- Educational level
- Linked to parent employee

### Trial Activation System
- **3-day trial period** from first launch
- **Hardware-based activation** using hard drive serial number
- **Automatic expiration** with application deletion
- **Real-time trial status** display in navigation

## 📋 Requirements

- **Python 3.8 or higher**
- **Web browser** (Chrome, Firefox, Safari, Edge)
- **Windows, macOS, or Linux**

## 🛠️ Installation & Setup

### Method 1: Quick Start (Windows)
1. Double-click `run.bat`
2. The script will automatically:
   - Check Python installation
   - Install required dependencies
   - Start the application
   - Open your web browser

### Method 2: Manual Setup
1. **Install Python** (if not already installed):
   - Download from [python.org](https://www.python.org/downloads/)
   - Make sure to check "Add Python to PATH" during installation

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the Application**:
   ```bash
   python app.py
   ```

4. **Open in Browser**:
   - Navigate to `http://127.0.0.1:5000`

## 🎯 Usage

### First Launch
- On first run, the application will automatically activate the 3-day trial
- The trial period is tied to your hard drive serial number
- Trial status is displayed in the navigation bar

### Managing Employees
1. **Add Employee**: Click "Add Employee" and fill in the required information
2. **Edit Employee**: Click the edit button (pencil icon) next to any employee
3. **Delete Employee**: Click the delete button (trash icon) and confirm
4. **View Details**: Click on employee name to view full details

### Managing Children
1. Navigate to an employee's record
2. Click "Manage Children" or the children count badge
3. Add children using the form at the top
4. Delete children using the delete button

### Trial Status
- **Green**: More than 1 day remaining
- **Yellow**: 1 day or less remaining
- **Red**: Trial expired

## 📁 File Structure

```
activation/
├── app.py                 # Main Flask application
├── models.py              # Database models
├── activation.py          # Trial activation system
├── requirements.txt       # Python dependencies
├── run.bat               # Windows startup script
├── README.md             # This file
├── templates/            # HTML templates
│   ├── base.html
│   ├── index.html
│   ├── employees.html
│   ├── employee_form.html
│   ├── children.html
│   └── activation_expired.html
└── static/               # CSS and JavaScript
    ├── css/
    │   └── style.css
    └── js/
        └── main.js
```

## 🔒 Security Features

### Trial Protection
- Hardware fingerprinting using HDD serial number
- Automatic application deletion after trial expiration
- Real-time activation status monitoring
- Tamper-resistant trial mechanism

### Data Security
- SQLite database with proper relationships
- Input validation and sanitization
- CSRF protection (Flask built-in)
- Secure session management

## 🎨 User Interface

### Modern Design
- **Bootstrap 5** for responsive layout
- **Font Awesome** icons for visual clarity
- **Gradient backgrounds** and smooth animations
- **Mobile-friendly** responsive design

### Key Pages
- **Dashboard**: Overview with statistics and trial status
- **Employee List**: Searchable and sortable table
- **Employee Form**: Comprehensive data entry
- **Children Management**: Linked child records
- **Trial Expired**: Automatic cleanup notification

## ⚙️ Technical Details

### Database Schema
```sql
-- Employees table
CREATE TABLE employees (
    id INTEGER PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    place_of_birth VARCHAR(200) NOT NULL,
    position VARCHAR(100) NOT NULL,
    workplace VARCHAR(200) NOT NULL,
    employment_date DATE NOT NULL,
    salary_amount FLOAT NOT NULL,
    bank_account_number VARCHAR(50) NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    agency VARCHAR(100) NOT NULL
);

-- Children table
CREATE TABLE children (
    id INTEGER PRIMARY KEY,
    employee_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    educational_level VARCHAR(100) NOT NULL,
    FOREIGN KEY (employee_id) REFERENCES employees (id)
);

-- Activation table
CREATE TABLE activation (
    id INTEGER PRIMARY KEY,
    install_date DATETIME NOT NULL,
    hdd_serial VARCHAR(100) NOT NULL,
    expiration_date DATETIME NOT NULL
);
```

### API Endpoints
- `GET /` - Dashboard
- `GET /employees` - Employee list
- `GET/POST /employee/add` - Add employee
- `GET/POST /employee/edit/<id>` - Edit employee
- `POST /employee/delete/<id>` - Delete employee
- `GET /children/<employee_id>` - Children management
- `POST /child/add/<employee_id>` - Add child
- `POST /child/delete/<id>` - Delete child
- `GET /api/activation-info` - Trial status API

## 🚨 Trial Expiration

When the 3-day trial expires:
1. **Automatic Detection**: Application checks trial status on every request
2. **Expiration Page**: User is redirected to a countdown page
3. **File Deletion**: Application files are automatically deleted
4. **Clean Exit**: Browser window closes automatically

## 🔧 Troubleshooting

### Common Issues

**Python not found**:
- Install Python from python.org
- Make sure "Add to PATH" is checked during installation
- Restart command prompt/terminal

**Dependencies installation fails**:
- Update pip: `python -m pip install --upgrade pip`
- Try: `python -m pip install -r requirements.txt`

**Application won't start**:
- Check if port 5000 is available
- Try running with: `python app.py`
- Check firewall settings

**Database errors**:
- Delete `database.db` file and restart
- Check file permissions in the application directory

### Support
This is a trial version. For support and full version licensing, please contact the development team.

## 📝 License

This is a trial version with the following restrictions:
- **3-day usage limit**
- **Automatic deletion** after trial expiration
- **No warranty or support** provided
- **For evaluation purposes only**

## 🔄 Version History

**v1.0.0** - Initial Release
- Complete employee management system
- Trial activation mechanism
- Modern web interface
- Automatic cleanup functionality

---

**⚠️ Important**: This is a trial version that will automatically delete itself after 3 days. Please backup any important data before the trial expires.
