@echo off
echo ========================================
echo Employee Management System - Startup
echo ========================================
echo.

echo Current directory: %CD%
echo.

echo Checking if we're in the right directory...
if not exist "app.py" (
    echo ERROR: app.py not found in current directory
    echo Please navigate to C:\Users\<USER>\Desktop\activation
    echo.
    pause
    exit /b 1
)

echo ✓ Found app.py
echo.

echo Checking Python installation...
py --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found
    pause
    exit /b 1
)

echo ✓ Python is working
echo.

echo Installing/checking dependencies...
py -m pip install Flask SQLAlchemy Flask-SQLAlchemy Werkzeug
echo.

echo Starting Employee Management System...
echo.
echo The application will be available at:
echo http://127.0.0.1:5000
echo.
echo Press Ctrl+C to stop the application
echo ========================================
echo.

py app.py

echo.
echo Application stopped.
pause
